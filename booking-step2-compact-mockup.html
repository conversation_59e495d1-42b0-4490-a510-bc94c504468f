<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid२Play - Compact Step 2 Slots</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            color: white;
            min-height: 100vh;
            padding: 16px;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: rgba(15, 23, 42, 0.9);
            border-radius: 20px;
            padding: 20px;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        /* Header */
        .header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: background 0.2s;
        }

        .back-btn:hover {
            background: rgba(16, 185, 129, 0.2);
        }

        .header-content h1 {
            color: #10b981;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 4px;
        }

        .step-indicator {
            color: #94a3b8;
            font-size: 14px;
        }

        /* Step Navigation */
        .step-nav {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .step-item {
            color: #64748b;
            font-size: 14px;
            font-weight: 500;
        }

        .step-item.active {
            color: #10b981;
            font-weight: 600;
        }

        /* Info Box */
        .info-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 16px;
            font-size: 13px;
            line-height: 1.4;
        }

        .tip {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            margin-top: 8px;
            font-size: 12px;
            color: #fbbf24;
        }

        /* Compact Slot Grid */
        .slots-container {
            margin-bottom: 20px;
        }

        .slots-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .slot-card {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid rgba(71, 85, 105, 0.5);
            border-radius: 12px;
            padding: 12px;
            cursor: pointer;
            transition: all 0.2s;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
        }

        .slot-card.available {
            border-color: rgba(16, 185, 129, 0.5);
            background: rgba(16, 185, 129, 0.05);
        }

        .slot-card.available:hover {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            transform: translateY(-1px);
        }

        .slot-card.selected {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.2);
            box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.3);
        }

        .slot-card.booked {
            border-color: rgba(239, 68, 68, 0.5);
            background: rgba(239, 68, 68, 0.1);
            cursor: not-allowed;
            opacity: 0.7;
        }

        .slot-time {
            font-size: 14px;
            font-weight: 600;
            color: white;
            margin-bottom: 2px;
        }

        .slot-duration {
            font-size: 11px;
            color: #94a3b8;
            margin-bottom: 8px;
        }

        .slot-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .slot-price {
            font-size: 14px;
            font-weight: 700;
            color: #10b981;
        }

        .slot-status {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            font-weight: 500;
        }

        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
        }

        .status-dot.available {
            background: #10b981;
        }

        .status-dot.booked {
            background: #ef4444;
        }

        .slot-status.available {
            color: #10b981;
        }

        .slot-status.booked {
            color: #ef4444;
        }

        /* Navigation Buttons */
        .nav-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .nav-btn {
            flex: 1;
            padding: 14px 20px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }

        .prev-btn {
            background: rgba(71, 85, 105, 0.3);
            color: white;
            border: 1px solid rgba(71, 85, 105, 0.5);
        }

        .prev-btn:hover {
            background: rgba(71, 85, 105, 0.5);
        }

        .next-btn {
            background: linear-gradient(135deg, #059669, #10b981);
            color: white;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }

        .next-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
        }

        .next-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Mobile Responsive */
        @media (max-width: 480px) {
            .container {
                margin: 0;
                border-radius: 0;
                min-height: 100vh;
            }

            .slots-grid {
                grid-template-columns: 1fr;
            }

            .slot-card {
                min-height: 70px;
                padding: 10px;
            }

            .slot-time {
                font-size: 13px;
            }

            .slot-price {
                font-size: 13px;
            }
        }

        /* Selection Summary */
        .selection-summary {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 16px;
            display: none;
        }

        .selection-summary.show {
            display: block;
        }

        .summary-text {
            font-size: 14px;
            color: #10b981;
            font-weight: 600;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <button class="back-btn">←</button>
            <div class="header-content">
                <h1>Book Your Slot</h1>
                <div class="step-indicator">Step 2 of 3</div>
            </div>
        </div>

        <!-- Step Navigation -->
        <div class="step-nav">
            <span class="step-item">Details</span>
            <span class="step-item active">Slots</span>
            <span class="step-item">Confirm</span>
        </div>

        <!-- Info Box -->
        <div class="info-box">
            You can only select consecutive (no gaps). Slots that would create gaps are automatically disabled.
            <div class="tip">
                <span>💡</span>
                <span>Tip: Hold Shift and click to select a range of consecutive slots</span>
            </div>
        </div>

        <!-- Selection Summary -->
        <div class="selection-summary" id="selectionSummary">
            <div class="summary-text" id="summaryText">2 slots selected • Total: ₹1300</div>
        </div>

        <!-- Compact Slots Grid -->
        <div class="slots-container">
            <div class="slots-grid">
                <div class="slot-card available selected" onclick="toggleSlot(this, '5:00 AM', 650)">
                    <div>
                        <div class="slot-time">5:00 AM</div>
                        <div class="slot-duration">to 5:30 AM</div>
                    </div>
                    <div class="slot-bottom">
                        <div class="slot-price">₹650</div>
                        <div class="slot-status available">
                            <div class="status-dot available"></div>
                            Available
                        </div>
                    </div>
                </div>

                <div class="slot-card booked">
                    <div>
                        <div class="slot-time">5:30 AM</div>
                        <div class="slot-duration">to 6:00 AM</div>
                    </div>
                    <div class="slot-bottom">
                        <div class="slot-price">₹650</div>
                        <div class="slot-status booked">
                            <div class="status-dot booked"></div>
                            Booked
                        </div>
                    </div>
                </div>

                <div class="slot-card booked">
                    <div>
                        <div class="slot-time">6:00 AM</div>
                        <div class="slot-duration">to 6:30 AM</div>
                    </div>
                    <div class="slot-bottom">
                        <div class="slot-price">₹650</div>
                        <div class="slot-status booked">
                            <div class="status-dot booked"></div>
                            Booked
                        </div>
                    </div>
                </div>

                <div class="slot-card available selected" onclick="toggleSlot(this, '6:30 AM', 650)">
                    <div>
                        <div class="slot-time">6:30 AM</div>
                        <div class="slot-duration">to 7:00 AM</div>
                    </div>
                    <div class="slot-bottom">
                        <div class="slot-price">₹650</div>
                        <div class="slot-status available">
                            <div class="status-dot available"></div>
                            Available
                        </div>
                    </div>
                </div>

                <div class="slot-card available" onclick="toggleSlot(this, '7:00 AM', 750)">
                    <div>
                        <div class="slot-time">7:00 AM</div>
                        <div class="slot-duration">to 7:30 AM</div>
                    </div>
                    <div class="slot-bottom">
                        <div class="slot-price">₹750</div>
                        <div class="slot-status available">
                            <div class="status-dot available"></div>
                            Available
                        </div>
                    </div>
                </div>

                <div class="slot-card available" onclick="toggleSlot(this, '7:30 AM', 750)">
                    <div>
                        <div class="slot-time">7:30 AM</div>
                        <div class="slot-duration">to 8:00 AM</div>
                    </div>
                    <div class="slot-bottom">
                        <div class="slot-price">₹750</div>
                        <div class="slot-status available">
                            <div class="status-dot available"></div>
                            Available
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="nav-buttons">
            <button class="nav-btn prev-btn">← Previous</button>
            <button class="nav-btn next-btn" id="nextBtn">Next →</button>
        </div>
    </div>

    <script>
        let selectedSlots = [];
        let totalAmount = 0;

        function toggleSlot(element, time, price) {
            if (element.classList.contains('booked')) return;
            
            if (element.classList.contains('selected')) {
                element.classList.remove('selected');
                selectedSlots = selectedSlots.filter(slot => slot.time !== time);
                totalAmount -= price;
            } else {
                element.classList.add('selected');
                selectedSlots.push({ time, price });
                totalAmount += price;
            }
            
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('selectionSummary');
            const summaryText = document.getElementById('summaryText');
            const nextBtn = document.getElementById('nextBtn');
            
            if (selectedSlots.length > 0) {
                summary.classList.add('show');
                summaryText.textContent = `${selectedSlots.length} slots selected • Total: ₹${totalAmount}`;
                nextBtn.disabled = false;
            } else {
                summary.classList.remove('show');
                nextBtn.disabled = true;
            }
        }

        // Initialize with 2 slots selected
        updateSummary();
    </script>
</body>
</html>
