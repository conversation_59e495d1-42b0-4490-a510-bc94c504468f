<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grid२Play - Smart Booking Step 1</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            color: white;
            min-height: 100vh;
            padding: 16px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Header */
        .header {
            text-align: center;
            margin-bottom: 24px;
        }

        .logo {
            background: linear-gradient(135deg, #059669, #10b981);
            padding: 12px 24px;
            border-radius: 16px;
            display: inline-block;
            font-weight: bold;
            font-size: 20px;
            margin-bottom: 16px;
            box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
        }

        .step-indicator {
            color: #10b981;
            font-size: 14px;
            font-weight: 600;
        }

        /* Smart Selection Row */
        .selection-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }

        .selector-card {
            background: rgba(15, 23, 42, 0.8);
            border: 2px solid rgba(16, 185, 129, 0.3);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .selector-card:hover {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
            transform: translateY(-2px);
        }

        .selector-card.selected {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.2);
        }

        .selector-label {
            font-size: 12px;
            color: #94a3b8;
            margin-bottom: 4px;
        }

        .selector-value {
            font-size: 14px;
            font-weight: 600;
            color: white;
        }

        /* Date Grid */
        .date-grid-container {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .date-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .date-nav {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            background: rgba(16, 185, 129, 0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }

        .nav-btn:hover {
            background: rgba(16, 185, 129, 0.4);
        }

        /* Grid Layout */
        .time-grid {
            display: grid;
            grid-template-columns: 80px repeat(4, 1fr);
            gap: 8px;
            align-items: center;
        }

        .date-column-header {
            text-align: center;
            padding: 12px 8px;
            background: rgba(16, 185, 129, 0.1);
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
        }

        .date-number {
            font-size: 16px;
            color: #10b981;
        }

        .date-day {
            font-size: 10px;
            color: #94a3b8;
        }

        .time-label {
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            color: #cbd5e1;
            padding: 8px 4px;
        }

        .slot-cell {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(71, 85, 105, 0.5);
            border-radius: 8px;
            padding: 8px 4px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            min-height: 48px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
        }

        .slot-cell.available {
            border-color: rgba(16, 185, 129, 0.5);
            background: rgba(16, 185, 129, 0.1);
        }

        .slot-cell.available:hover {
            background: rgba(16, 185, 129, 0.2);
            transform: scale(1.02);
        }

        .slot-cell.selected {
            background: #10b981;
            border-color: #059669;
            color: white;
        }

        .slot-cell.booked {
            background: rgba(239, 68, 68, 0.2);
            border-color: rgba(239, 68, 68, 0.5);
            cursor: not-allowed;
        }

        .slot-price {
            font-size: 11px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .slot-availability {
            font-size: 9px;
            color: #94a3b8;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .selection-row {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .selector-card {
                min-height: 60px;
                padding: 12px;
            }

            .time-grid {
                grid-template-columns: 60px repeat(3, 1fr);
                gap: 4px;
            }

            .slot-cell {
                min-height: 44px;
                padding: 6px 2px;
            }

            .slot-price {
                font-size: 10px;
            }

            .slot-availability {
                font-size: 8px;
            }

            .date-column-header {
                padding: 8px 4px;
                font-size: 11px;
            }
        }

        /* Continue Button */
        .continue-btn {
            background: linear-gradient(135deg, #059669, #10b981);
            border: none;
            color: white;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 24px;
            transition: all 0.3s;
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3);
        }

        .continue-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.4);
        }

        .continue-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        /* Selection Summary */
        .selection-summary {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
            display: none;
        }

        .selection-summary.show {
            display: block;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .summary-total {
            border-top: 1px solid rgba(16, 185, 129, 0.3);
            padding-top: 8px;
            font-weight: 600;
            color: #10b981;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo">Grid२Play</div>
            <div class="step-indicator">Step 1 of 2 - Smart Selection</div>
        </div>

        <!-- Smart Selection Row -->
        <div class="selection-row">
            <div class="selector-card selected" onclick="toggleSelector(this)">
                <div class="selector-label">Venue</div>
                <div class="selector-value">SportZone Arena</div>
            </div>
            <div class="selector-card selected" onclick="toggleSelector(this)">
                <div class="selector-label">Sport</div>
                <div class="selector-value">Badminton</div>
            </div>
            <div class="selector-card selected" onclick="toggleSelector(this)">
                <div class="selector-label">Court</div>
                <div class="selector-value">Court A1</div>
            </div>
        </div>

        <!-- Date Grid Container -->
        <div class="date-grid-container">
            <div class="date-header">
                <h3 style="color: #10b981; font-size: 18px;">Select Date & Time</h3>
                <div class="date-nav">
                    <button class="nav-btn" onclick="previousDates()">‹</button>
                    <button class="nav-btn" onclick="nextDates()">›</button>
                </div>
            </div>

            <!-- Time Grid -->
            <div class="time-grid">
                <!-- Header Row -->
                <div></div>
                <div class="date-column-header">
                    <div class="date-number">26</div>
                    <div class="date-day">Today</div>
                </div>
                <div class="date-column-header">
                    <div class="date-number">27</div>
                    <div class="date-day">Tomorrow</div>
                </div>
                <div class="date-column-header">
                    <div class="date-number">28</div>
                    <div class="date-day">Mon</div>
                </div>
                <div class="date-column-header">
                    <div class="date-number">29</div>
                    <div class="date-day">Tue</div>
                </div>

                <!-- Time Slots -->
                <div class="time-label">06:00<br>AM</div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹150</div>
                    <div class="slot-availability">4 left</div>
                </div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹150</div>
                    <div class="slot-availability">3 left</div>
                </div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹150</div>
                    <div class="slot-availability">2 left</div>
                </div>
                <div class="slot-cell booked">
                    <div class="slot-price">₹150</div>
                    <div class="slot-availability">Booked</div>
                </div>

                <div class="time-label">07:00<br>AM</div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹200</div>
                    <div class="slot-availability">5 left</div>
                </div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹200</div>
                    <div class="slot-availability">4 left</div>
                </div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹200</div>
                    <div class="slot-availability">3 left</div>
                </div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹200</div>
                    <div class="slot-availability">2 left</div>
                </div>

                <div class="time-label">08:00<br>AM</div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹250</div>
                    <div class="slot-availability">6 left</div>
                </div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹250</div>
                    <div class="slot-availability">5 left</div>
                </div>
                <div class="slot-cell booked">
                    <div class="slot-price">₹250</div>
                    <div class="slot-availability">Booked</div>
                </div>
                <div class="slot-cell available" onclick="selectSlot(this)">
                    <div class="slot-price">₹250</div>
                    <div class="slot-availability">1 left</div>
                </div>
            </div>
        </div>

        <!-- Selection Summary -->
        <div class="selection-summary" id="selectionSummary">
            <div class="summary-item">
                <span>Selected Slots:</span>
                <span id="selectedCount">0</span>
            </div>
            <div class="summary-item summary-total">
                <span>Total Amount:</span>
                <span id="totalAmount">₹0</span>
            </div>
        </div>

        <!-- Continue Button -->
        <button class="continue-btn" id="continueBtn" disabled onclick="proceedToPayment()">
            Continue to Payment & Guest Details
        </button>
    </div>

    <script>
        let selectedSlots = [];
        let totalAmount = 0;

        function toggleSelector(element) {
            // Toggle selection visual feedback
            element.classList.toggle('selected');
        }

        function selectSlot(element) {
            if (element.classList.contains('booked')) return;
            
            const price = parseInt(element.querySelector('.slot-price').textContent.replace('₹', ''));
            
            if (element.classList.contains('selected')) {
                // Deselect
                element.classList.remove('selected');
                selectedSlots = selectedSlots.filter(slot => slot.element !== element);
                totalAmount -= price;
            } else {
                // Select
                element.classList.add('selected');
                selectedSlots.push({ element, price });
                totalAmount += price;
            }
            
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('selectionSummary');
            const continueBtn = document.getElementById('continueBtn');
            const selectedCount = document.getElementById('selectedCount');
            const totalAmountEl = document.getElementById('totalAmount');
            
            if (selectedSlots.length > 0) {
                summary.classList.add('show');
                continueBtn.disabled = false;
                selectedCount.textContent = selectedSlots.length;
                totalAmountEl.textContent = `₹${totalAmount}`;
            } else {
                summary.classList.remove('show');
                continueBtn.disabled = true;
            }
        }

        function previousDates() {
            // Implement date navigation
            console.log('Previous dates');
        }

        function nextDates() {
            // Implement date navigation
            console.log('Next dates');
        }

        function proceedToPayment() {
            alert(`Proceeding to Step 2 with ${selectedSlots.length} slots selected. Total: ₹${totalAmount}`);
        }
    </script>
</body>
</html>
